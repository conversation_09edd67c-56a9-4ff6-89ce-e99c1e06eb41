/* 重置样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 根变量 */
:root {
    --primary-bg: #0a0a0a;
    --secondary-bg: #1a1a1a;
    --card-bg: rgba(26, 26, 26, 0.95);
    --border-color: rgba(255, 255, 255, 0.1);
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --text-muted: #888888;
    --accent-blue: #00d4ff;
    --accent-purple: #9d4edd;
    --accent-gold: #ffd700;
    --accent-red: #ff4757;
    --accent-green: #2ed573;
    
    /* 属性颜色 */
    --thunder-color: #00d4ff;
    --ice-color: #4cc9f0;
    --fire-color: #f72585;
    --wind-color: #43aa8b;
    --light-color: #f9c74f;
    --dark-color: #9d4edd;
    
    /* 评级颜色 */
    --tier-s: linear-gradient(135deg, #ffd700, #ffed4e);
    --tier-a: linear-gradient(135deg, #ff6b35, #ff8e53);
    --tier-b: linear-gradient(135deg, #4cc9f0, #7209b7);
    --tier-c: linear-gradient(135deg, #43aa8b, #90e0ef);
}

/* 基础样式 */
body {
    font-family: 'Noto Sans SC', 'Microsoft YaHei', 'PingFang SC', Arial, sans-serif;
    background: var(--primary-bg);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
    min-height: 100vh;
}

/* 背景装饰 */
.bg-decoration {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 20% 20%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(157, 78, 221, 0.1) 0%, transparent 50%),
        repeating-linear-gradient(
            45deg,
            transparent,
            transparent 40px,
            rgba(255,255,255,0.02) 40px,
            rgba(255,255,255,0.02) 80px
        );
    z-index: -1;
    pointer-events: none;
}

/* 容器 */
.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 头部样式 */
.header {
    padding: 30px 0;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 30px;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.logo-section {
    flex: 1;
}

.main-title {
    font-size: 2.5rem;
    font-weight: 700;
    background: linear-gradient(135deg, var(--accent-blue), var(--accent-purple));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 8px;
}

.subtitle {
    font-size: 1.1rem;
    color: var(--text-muted);
    font-weight: 300;
}

.version-info {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 8px;
}

.version-tag {
    background: var(--tier-s);
    color: #000;
    padding: 6px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
}

.update-time {
    color: var(--text-muted);
    font-size: 0.85rem;
}

/* 导航标签 */
.nav-tabs {
    display: flex;
    gap: 2px;
    background: var(--secondary-bg);
    border-radius: 12px;
    padding: 4px;
}

.nav-tab {
    flex: 1;
    padding: 12px 24px;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.95rem;
    font-weight: 500;
}

.nav-tab:hover {
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-primary);
}

.nav-tab.active {
    background: var(--accent-blue);
    color: #000;
    font-weight: 600;
}

/* 控制面板 */
.controls {
    background: var(--card-bg);
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 30px;
    border: 1px solid var(--border-color);
}

.filter-section {
    display: flex;
    gap: 30px;
    margin-bottom: 20px;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.filter-group label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.attribute-filters,
.weapon-filters {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 8px 16px;
    border: 1px solid var(--border-color);
    background: var(--secondary-bg);
    color: var(--text-secondary);
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.85rem;
    font-weight: 500;
}

.filter-btn:hover {
    border-color: var(--accent-blue);
    color: var(--text-primary);
}

.filter-btn.active {
    background: var(--accent-blue);
    border-color: var(--accent-blue);
    color: #000;
}

/* 属性特定颜色 */
.filter-btn.thunder.active { background: var(--thunder-color); }
.filter-btn.ice.active { background: var(--ice-color); }
.filter-btn.fire.active { background: var(--fire-color); }
.filter-btn.wind.active { background: var(--wind-color); }
.filter-btn.light.active { background: var(--light-color); }
.filter-btn.dark.active { background: var(--dark-color); }

.search-section {
    display: flex;
    justify-content: flex-end;
}

.search-input {
    padding: 12px 20px;
    border: 1px solid var(--border-color);
    background: var(--secondary-bg);
    color: var(--text-primary);
    border-radius: 25px;
    width: 300px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.search-input:focus {
    outline: none;
    border-color: var(--accent-blue);
    box-shadow: 0 0 0 3px rgba(0, 212, 255, 0.1);
}

.search-input::placeholder {
    color: var(--text-muted);
}

/* 排行榜表格 */
.ranking-table-container {
    background: var(--card-bg);
    border-radius: 15px;
    overflow: hidden;
    border: 1px solid var(--border-color);
    margin-bottom: 30px;
}

.ranking-table {
    width: 100%;
    border-collapse: collapse;
}

.ranking-table th {
    background: var(--secondary-bg);
    padding: 18px 15px;
    text-align: left;
    font-weight: 600;
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-color);
    position: relative;
}

.ranking-table th.sortable {
    cursor: pointer;
    user-select: none;
    transition: background-color 0.3s ease;
}

.ranking-table th.sortable:hover {
    background: rgba(255, 255, 255, 0.05);
}

.sort-arrow {
    margin-left: 8px;
    font-size: 0.8rem;
    opacity: 0.6;
}

.ranking-table td {
    padding: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    vertical-align: middle;
}

.ranking-table tbody tr {
    transition: background-color 0.3s ease;
    cursor: pointer;
}

.ranking-table tbody tr:hover {
    background: rgba(255, 255, 255, 0.03);
}

/* 表格列样式 */
.rank-col { width: 80px; text-align: center; }
.character-col { width: 200px; }
.attribute-col { width: 100px; text-align: center; }
.weapon-col { width: 120px; text-align: center; }
.score-col { width: 100px; text-align: center; }
.tier-col { width: 100px; text-align: center; }

/* 角色信息 */
.character-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.character-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--accent-blue), var(--accent-purple));
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 1.2rem;
    color: #000;
}

.character-details h4 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 4px;
}

.character-details p {
    font-size: 0.8rem;
    color: var(--text-muted);
}

/* 属性标签 */
.attribute-tag {
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    text-align: center;
    color: #000;
}

.attribute-tag.thunder { background: var(--thunder-color); }
.attribute-tag.ice { background: var(--ice-color); }
.attribute-tag.fire { background: var(--fire-color); }
.attribute-tag.wind { background: var(--wind-color); }
.attribute-tag.light { background: var(--light-color); }
.attribute-tag.dark { background: var(--dark-color); }

/* 评分显示 */
.score-display {
    font-weight: 600;
    font-size: 1.1rem;
}

.score-display.excellent { color: var(--accent-gold); }
.score-display.good { color: var(--accent-green); }
.score-display.average { color: var(--accent-blue); }
.score-display.poor { color: var(--text-muted); }

/* 评级标签 */
.tier-badge {
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 700;
    font-size: 0.9rem;
    text-align: center;
    color: #000;
}

.tier-badge.S { background: var(--tier-s); }
.tier-badge.A { background: var(--tier-a); }
.tier-badge.B { background: var(--tier-b); }
.tier-badge.C { background: var(--tier-c); }

/* 响应式设计 */
@media (max-width: 1200px) {
    .container {
        padding: 0 15px;
    }
    
    .main-title {
        font-size: 2rem;
    }
    
    .filter-section {
        flex-direction: column;
        gap: 20px;
    }
}

@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 20px;
    }
    
    .version-info {
        align-items: flex-start;
    }
    
    .nav-tabs {
        flex-wrap: wrap;
    }
    
    .nav-tab {
        flex: 1 1 calc(50% - 1px);
        min-width: 120px;
    }
    
    .search-input {
        width: 100%;
    }
    
    .ranking-table-container {
        overflow-x: auto;
    }
    
    .ranking-table {
        min-width: 800px;
    }
}

@media (max-width: 480px) {
    .main-title {
        font-size: 1.5rem;
    }

    .nav-tab {
        flex: 1 1 100%;
        margin-bottom: 2px;
    }

    .character-info {
        flex-direction: column;
        text-align: center;
        gap: 8px;
    }

    .character-avatar {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
}

/* 模态框样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: var(--card-bg);
    border-radius: 20px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.modal-overlay.active .modal-content {
    transform: scale(1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25px 30px;
    border-bottom: 1px solid var(--border-color);
}

.modal-title {
    font-size: 1.5rem;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    color: var(--text-muted);
    font-size: 2rem;
    cursor: pointer;
    transition: color 0.3s ease;
    padding: 0;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    color: var(--text-primary);
}

.modal-body {
    padding: 30px;
}

.character-detail-grid {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 30px;
    margin-bottom: 30px;
}

.character-detail-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--accent-blue), var(--accent-purple));
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    font-weight: 700;
    color: #000;
    margin: 0 auto;
}

.character-detail-info h3 {
    font-size: 1.8rem;
    margin-bottom: 10px;
}

.character-detail-info p {
    color: var(--text-secondary);
    margin-bottom: 15px;
    line-height: 1.6;
}

.character-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-item {
    background: var(--secondary-bg);
    padding: 20px;
    border-radius: 12px;
    border: 1px solid var(--border-color);
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-muted);
    margin-bottom: 8px;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 8px;
}

.stat-bar {
    width: 100%;
    height: 6px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    overflow: hidden;
}

.stat-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple));
    border-radius: 3px;
    transition: width 0.8s ease;
}

/* 页脚样式 */
.footer {
    margin-top: 50px;
    padding: 30px 0;
    border-top: 1px solid var(--border-color);
    background: var(--secondary-bg);
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.footer-content p {
    color: var(--text-muted);
    font-size: 0.9rem;
}

.share-section {
    display: flex;
    align-items: center;
    gap: 15px;
}

.share-section span {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.share-btn {
    padding: 8px 16px;
    border: 1px solid var(--accent-blue);
    background: transparent;
    color: var(--accent-blue);
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.85rem;
}

.share-btn:hover {
    background: var(--accent-blue);
    color: #000;
}

/* 提示消息 */
.toast {
    position: fixed;
    bottom: 30px;
    right: 30px;
    background: var(--card-bg);
    color: var(--text-primary);
    padding: 15px 25px;
    border-radius: 10px;
    border: 1px solid var(--border-color);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    transform: translateY(100px);
    opacity: 0;
    transition: all 0.3s ease;
    z-index: 1001;
}

.toast.show {
    transform: translateY(0);
    opacity: 1;
}

.toast-message {
    font-size: 0.9rem;
}

/* 加载动画 */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 50px;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 255, 255, 0.1);
    border-top: 3px solid var(--accent-blue);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: var(--text-muted);
}

.empty-state h3 {
    font-size: 1.2rem;
    margin-bottom: 10px;
}

.empty-state p {
    font-size: 0.9rem;
}

/* 角色详情额外样式 */
.character-description {
    margin-bottom: 25px;
}

.character-description h4 {
    font-size: 1.2rem;
    margin-bottom: 10px;
    color: var(--text-primary);
}

.character-description p {
    line-height: 1.6;
    color: var(--text-secondary);
}

.character-skills {
    margin-bottom: 25px;
}

.character-skills h4 {
    font-size: 1.2rem;
    margin-bottom: 15px;
    color: var(--text-primary);
}

.character-skills ul {
    list-style: none;
    padding: 0;
}

.character-skills li {
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    color: var(--text-secondary);
}

.character-skills li:last-child {
    border-bottom: none;
}

.character-pros-cons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 25px;
}

.pros h4 {
    color: var(--accent-green);
    font-size: 1.1rem;
    margin-bottom: 10px;
}

.cons h4 {
    color: var(--accent-red);
    font-size: 1.1rem;
    margin-bottom: 10px;
}

.pros ul,
.cons ul {
    list-style: none;
    padding: 0;
}

.pros li,
.cons li {
    padding: 6px 0;
    color: var(--text-secondary);
    position: relative;
    padding-left: 20px;
}

.pros li::before {
    content: "✓";
    position: absolute;
    left: 0;
    color: var(--accent-green);
    font-weight: bold;
}

.cons li::before {
    content: "✗";
    position: absolute;
    left: 0;
    color: var(--accent-red);
    font-weight: bold;
}

.rank-number {
    font-weight: 600;
    font-size: 1.1rem;
}

/* 排名特殊样式 */
.rank-number {
    display: inline-block;
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    border-radius: 50%;
    font-weight: 600;
}

tr:nth-child(1) .rank-number {
    background: var(--tier-s);
    color: #000;
}

tr:nth-child(2) .rank-number {
    background: var(--tier-a);
    color: #000;
}

tr:nth-child(3) .rank-number {
    background: var(--tier-b);
    color: #000;
}

/* 响应式模态框 */
@media (max-width: 768px) {
    .modal-content {
        width: 95%;
        margin: 20px;
    }

    .character-detail-grid {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 20px;
    }

    .character-stats {
        grid-template-columns: 1fr;
    }

    .character-pros-cons {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .modal-header {
        padding: 20px;
    }

    .modal-body {
        padding: 20px;
    }
}
