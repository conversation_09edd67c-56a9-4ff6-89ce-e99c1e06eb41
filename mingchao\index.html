<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>鸣潮角色强度排行榜 - Wuthering Waves Character Tier List</title>
    <meta name="description" content="鸣潮游戏角色强度排行榜，包含DPS输出、生存能力、辅助能力等多维度评估">
    <meta name="keywords" content="鸣潮,Wuthering Waves,角色排行榜,强度评估,游戏攻略">
    
    <!-- Open Graph Meta Tags for sharing -->
    <meta property="og:title" content="鸣潮角色强度排行榜">
    <meta property="og:description" content="专业的鸣潮角色强度分析与排行榜">
    <meta property="og:type" content="website">
    <meta property="og:image" content="./assets/og-image.jpg">
    
    <link rel="stylesheet" href="./styles/main.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- 背景装饰 -->
    <div class="bg-decoration"></div>
    
    <!-- 主容器 -->
    <div class="container">
        <!-- 头部 -->
        <header class="header">
            <div class="header-content">
                <div class="logo-section">
                    <h1 class="main-title">鸣潮角色强度排行榜</h1>
                    <p class="subtitle">Wuthering Waves Character Tier List</p>
                </div>
                
                <div class="version-info">
                    <span class="version-tag">1.4版本</span>
                    <span class="update-time">更新时间: 2024-12-19</span>
                </div>
            </div>
            
            <!-- 导航栏 -->
            <nav class="nav-tabs">
                <button class="nav-tab active" data-tab="ranking">综合排行</button>
                <button class="nav-tab" data-tab="dps">DPS输出</button>
                <button class="nav-tab" data-tab="support">辅助能力</button>
                <button class="nav-tab" data-tab="survival">生存能力</button>
            </nav>
        </header>
        
        <!-- 筛选控制 -->
        <div class="controls">
            <div class="filter-section">
                <div class="filter-group">
                    <label>属性筛选:</label>
                    <div class="attribute-filters">
                        <button class="filter-btn active" data-filter="all">全部</button>
                        <button class="filter-btn thunder" data-filter="thunder">雷</button>
                        <button class="filter-btn ice" data-filter="ice">冰</button>
                        <button class="filter-btn fire" data-filter="fire">焰</button>
                        <button class="filter-btn wind" data-filter="wind">风</button>
                        <button class="filter-btn light" data-filter="light">光</button>
                        <button class="filter-btn dark" data-filter="dark">暗</button>
                    </div>
                </div>
                
                <div class="filter-group">
                    <label>武器类型:</label>
                    <div class="weapon-filters">
                        <button class="filter-btn active" data-weapon="all">全部</button>
                        <button class="filter-btn" data-weapon="sword">剑</button>
                        <button class="filter-btn" data-weapon="gun">枪</button>
                        <button class="filter-btn" data-weapon="gauntlet">拳套</button>
                        <button class="filter-btn" data-weapon="rectifier">变奏器</button>
                        <button class="filter-btn" data-weapon="broadblade">阔刀</button>
                    </div>
                </div>
            </div>
            
            <div class="search-section">
                <input type="text" id="searchInput" placeholder="搜索角色名称..." class="search-input">
            </div>
        </div>
        
        <!-- 排行榜内容 -->
        <main class="ranking-content">
            <!-- 排行榜表格 -->
            <div class="ranking-table-container">
                <table class="ranking-table" id="rankingTable">
                    <thead>
                        <tr>
                            <th class="rank-col">排名</th>
                            <th class="character-col">角色</th>
                            <th class="attribute-col">属性</th>
                            <th class="weapon-col">武器</th>
                            <th class="score-col sortable" data-sort="overall">
                                综合评分 <span class="sort-arrow">↓</span>
                            </th>
                            <th class="score-col sortable" data-sort="dps">
                                DPS <span class="sort-arrow"></span>
                            </th>
                            <th class="score-col sortable" data-sort="support">
                                辅助 <span class="sort-arrow"></span>
                            </th>
                            <th class="score-col sortable" data-sort="survival">
                                生存 <span class="sort-arrow"></span>
                            </th>
                            <th class="tier-col">评级</th>
                        </tr>
                    </thead>
                    <tbody id="rankingTableBody">
                        <!-- 数据将通过JavaScript动态生成 -->
                    </tbody>
                </table>
            </div>
            
            <!-- 角色详情模态框 -->
            <div class="modal-overlay" id="characterModal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3 class="modal-title" id="modalTitle">角色详情</h3>
                        <button class="modal-close" id="modalClose">×</button>
                    </div>
                    <div class="modal-body" id="modalBody">
                        <!-- 详情内容将动态生成 -->
                    </div>
                </div>
            </div>
        </main>
        
        <!-- 页脚 -->
        <footer class="footer">
            <div class="footer-content">
                <p>© 2024 鸣潮角色强度排行榜 | 数据来源于游戏实测与社区贡献</p>
                <div class="share-section">
                    <span>分享链接:</span>
                    <button class="share-btn" id="shareBtn">复制链接</button>
                    <button class="share-btn" id="exportBtn">导出数据</button>
                </div>
            </div>
        </footer>
    </div>
    
    <!-- 提示消息 -->
    <div class="toast" id="toast">
        <span class="toast-message" id="toastMessage"></span>
    </div>
    
    <script src="./scripts/data.js"></script>
    <script src="./scripts/main.js"></script>
</body>
</html>
