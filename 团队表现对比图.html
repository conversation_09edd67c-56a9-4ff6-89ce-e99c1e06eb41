<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>团队表现对比图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .chart-container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 1000px;
            margin: 0 auto;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .mermaid {
            text-align: center;
        }
        .legend {
            margin-top: 20px;
            text-align: center;
            font-size: 14px;
            color: #666;
        }
        .legend-item {
            display: inline-block;
            margin: 0 20px;
        }
        .legend-color {
            display: inline-block;
            width: 20px;
            height: 3px;
            margin-right: 8px;
            vertical-align: middle;
        }
        .blue-line {
            background-color: #4A90E2;
        }
        .orange-line {
            background-color: #F5A623;
        }
    </style>
</head>
<body>
    <div class="chart-container">
        <h1>团队表现对比图</h1>
        
        <div class="mermaid">
            xychart-beta
                title "主力队 + 替补队员: 85 + 15"
                x-axis [春季, 夏季, 秋季, 冬季, 新年, 元宵, 清明, 端午, 中秋, 国庆, 双十一]
                y-axis "表现指数" 0 --> 4
                line [1.1, 0.8, 0.9, 1.6, 1.8, 1.3, 1.1, 1.2, 0.9, 1.5, 1.6]
                line [2.0, 2.1, 2.3, 3.2, 3.6, 2.5, 2.6, 2.4, 1.8, 2.0, 2.1]
        </div>
        
        <div class="legend">
            <div class="legend-item">
                <span class="legend-color blue-line"></span>
                基础表现指数
            </div>
            <div class="legend-item">
                <span class="legend-color orange-line"></span>
                综合表现指数
            </div>
        </div>
    </div>

    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            themeVariables: {
                primaryColor: '#4A90E2',
                primaryTextColor: '#333',
                primaryBorderColor: '#4A90E2',
                lineColor: '#F5A623'
            }
        });
    </script>
</body>
</html>
