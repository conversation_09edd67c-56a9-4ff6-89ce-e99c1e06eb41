<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>能效曲线 - 鸣潮角色DPS分析</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', Arial, sans-serif;
            background: #0a0a0a;
            color: white;
            overflow-x: hidden;
            min-height: 100vh;
        }
        
        /* 背景纹理 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                repeating-linear-gradient(
                    45deg,
                    transparent,
                    transparent 40px,
                    rgba(255,255,255,0.02) 40px,
                    rgba(255,255,255,0.02) 80px
                ),
                radial-gradient(circle at 20% 20%, rgba(0, 212, 255, 0.1) 0%, transparent 50%);
            z-index: -1;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .back-btn {
            position: absolute;
            top: 20px;
            left: 20px;
            width: 40px;
            height: 40px;
            border: 2px solid #555;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: #888;
            font-size: 18px;
            transition: all 0.3s ease;
        }
        
        .back-btn:hover {
            border-color: #00d4ff;
            color: #00d4ff;
        }
        
        .header {
            text-align: center;
            margin: 60px 0 30px 0;
        }
        
        .title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .subtitle {
            font-size: 14px;
            color: #888;
            margin-bottom: 20px;
        }
        
        .update-tags {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-bottom: 30px;
        }
        
        .tag {
            padding: 6px 16px;
            border-radius: 20px;
            font-size: 12px;
            border: 1px solid;
        }
        
        .tag.primary {
            background: rgba(0, 212, 255, 0.2);
            border-color: #00d4ff;
            color: #00d4ff;
        }
        
        .tag.secondary {
            background: rgba(255, 107, 53, 0.2);
            border-color: #ff6b35;
            color: #ff6b35;
        }
        
        .chart-container {
            background: rgba(10, 10, 10, 0.95);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .chart-title {
            font-size: 16px;
            color: #ccc;
            margin-bottom: 20px;
        }
        
        .chart-area {
            width: 100%;
            height: 400px;
            position: relative;
            background-image: 
                linear-gradient(to right, rgba(255,255,255,0.1) 1px, transparent 1px),
                linear-gradient(to bottom, rgba(255,255,255,0.1) 1px, transparent 1px);
            background-size: 60px 40px;
        }
        
        .axis-y {
            position: absolute;
            left: -40px;
            height: 100%;
            display: flex;
            flex-direction: column-reverse;
            justify-content: space-between;
            align-items: flex-end;
            color: #888;
            font-size: 12px;
        }
        
        .axis-x {
            position: absolute;
            bottom: -25px;
            width: 100%;
            display: flex;
            justify-content: space-between;
            color: #888;
            font-size: 12px;
        }
        
        .axis-label-x {
            position: absolute;
            bottom: -50px;
            right: 0;
            color: #888;
            font-size: 14px;
        }
        
        .curve-svg {
            position: absolute;
            width: 100%;
            height: 100%;
        }
        
        .curve-path {
            fill: none;
            stroke-width: 3;
            filter: drop-shadow(0 0 6px currentColor);
            transition: opacity 0.3s ease;
        }
        
        .curve-path.hidden {
            opacity: 0;
        }
        
        .data-label {
            font-size: 11px;
            fill: currentColor;
            text-anchor: middle;
        }
        
        .controls {
            margin-top: 30px;
        }
        
        .control-section {
            margin-bottom: 25px;
        }
        
        .section-title {
            font-size: 16px;
            color: #ccc;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 1px solid #333;
        }
        
        .control-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .control-btn {
            padding: 8px 16px;
            border-radius: 20px;
            border: 1px solid #444;
            background: rgba(26, 26, 26, 0.8);
            color: #ccc;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .control-btn:hover {
            border-color: #666;
            background: rgba(40, 40, 40, 0.8);
        }
        
        .control-btn.active {
            border-color: var(--color);
            background: var(--bg-color);
            color: white;
            box-shadow: 0 0 10px var(--glow-color);
        }
        
        .control-btn.inactive {
            opacity: 0.5;
            background: rgba(60, 60, 60, 0.5);
        }
        
        /* 角色按钮颜色 */
        .jinxi { --color: #00d4ff; --bg-color: rgba(0, 212, 255, 0.3); --glow-color: rgba(0, 212, 255, 0.3); }
        .changli { --color: #4cc9f0; --bg-color: rgba(76, 201, 240, 0.3); --glow-color: rgba(76, 201, 240, 0.3); }
        .chixia { --color: #f72585; --bg-color: rgba(247, 37, 133, 0.3); --glow-color: rgba(247, 37, 133, 0.3); }
        .kakaro { --color: #9d4edd; --bg-color: rgba(157, 78, 221, 0.3); --glow-color: rgba(157, 78, 221, 0.3); }
        .yinlin { --color: #43aa8b; --bg-color: rgba(67, 170, 139, 0.3); --glow-color: rgba(67, 170, 139, 0.3); }
        .weilinaer { --color: #f9c74f; --bg-color: rgba(249, 199, 79, 0.3); --glow-color: rgba(249, 199, 79, 0.3); }
    </style>
</head>
<body>
    <div class="container">
        <div class="back-btn">←</div>
        
        <div class="header">
            <h1 class="title">能效曲线 - 鸣潮角色DPS分析</h1>
            <p class="subtitle">基于实战测试数据，展示不同共鸣链数下的角色DPS表现，数据来源于游戏实测与理论计算</p>
            
            <div class="update-tags">
                <div class="tag primary">数据更新</div>
                <div class="tag secondary">查看 1.4版本 角色数据</div>
            </div>
        </div>
        
        <div class="chart-container">
            <div class="chart-title">DPS输出 多核 (万)</div>
            
            <div class="chart-area">
                <div class="axis-y">
                    <span>120</span>
                    <span>100</span>
                    <span>80</span>
                    <span>60</span>
                    <span>40</span>
                    <span>20</span>
                    <span>0</span>
                </div>
                
                <div class="axis-x">
                    <span>0</span>
                    <span>2</span>
                    <span>4</span>
                    <span>6</span>
                    <span>8</span>
                    <span>10</span>
                    <span>12</span>
                    <span>14</span>
                    <span>16</span>
                    <span>18</span>
                    <span>20</span>
                    <span>22</span>
                </div>
                
                <div class="axis-label-x">共鸣链数 (链)</div>
                
                <svg class="curve-svg" viewBox="0 0 1000 400">
                    <!-- 今汐曲线 -->
                    <path id="jinxi-curve" class="curve-path" 
                          d="M 50 350 Q 150 300 250 250 Q 400 180 550 120 Q 700 80 850 50 Q 900 40 950 35" 
                          stroke="#00d4ff"/>
                    <text class="data-label" x="950" y="30" fill="#00d4ff">今汐</text>
                    
                    <!-- 长离曲线 -->
                    <path id="changli-curve" class="curve-path" 
                          d="M 50 360 Q 150 320 250 270 Q 400 200 550 140 Q 700 100 850 70 Q 900 60 950 55" 
                          stroke="#4cc9f0"/>
                    <text class="data-label" x="950" y="50" fill="#4cc9f0">长离</text>
                    
                    <!-- 炽霞曲线 -->
                    <path id="chixia-curve" class="curve-path" 
                          d="M 50 370 Q 150 340 250 300 Q 400 240 550 180 Q 700 140 850 110 Q 900 100 950 95" 
                          stroke="#f72585"/>
                    <text class="data-label" x="950" y="90" fill="#f72585">炽霞</text>
                    
                    <!-- 卡卡罗曲线 -->
                    <path id="kakaro-curve" class="curve-path" 
                          d="M 50 380 Q 150 350 250 320 Q 400 260 550 200 Q 700 160 850 130 Q 900 120 950 115" 
                          stroke="#9d4edd"/>
                    <text class="data-label" x="950" y="110" fill="#9d4edd">卡卡罗</text>
                    
                    <!-- 吟霖曲线 -->
                    <path id="yinlin-curve" class="curve-path" 
                          d="M 50 390 Q 150 360 250 340 Q 400 280 550 220 Q 700 180 850 150 Q 900 140 950 135" 
                          stroke="#43aa8b"/>
                    <text class="data-label" x="950" y="130" fill="#43aa8b">吟霖</text>
                    
                    <!-- 维里奈曲线 -->
                    <path id="weilinaer-curve" class="curve-path" 
                          d="M 50 395 Q 150 370 250 350 Q 400 290 550 240 Q 700 200 850 170 Q 900 160 950 155" 
                          stroke="#f9c74f"/>
                    <text class="data-label" x="950" y="150" fill="#f9c74f">维里奈</text>
                </svg>
            </div>
        </div>
        
        <div class="controls">
            <div class="control-section">
                <div class="section-title">高端输出角色</div>
                <div class="control-buttons">
                    <div class="control-btn jinxi active" data-curve="jinxi-curve">
                        今汐 雷属性<br>
                        <small>LPDDR5X 8533 / 共鸣链满级</small>
                    </div>
                    <div class="control-btn changli active" data-curve="changli-curve">
                        长离 冰属性<br>
                        <small>LPDDR5X 8533 / 共鸣链满级</small>
                    </div>
                    <div class="control-btn chixia active" data-curve="chixia-curve">
                        炽霞 焰属性<br>
                        <small>LPDDR5X 8533 / 共鸣链满级</small>
                    </div>
                </div>
            </div>
            
            <div class="control-section">
                <div class="section-title">高端输出及辅助</div>
                <div class="control-buttons">
                    <div class="control-btn kakaro active" data-curve="kakaro-curve">
                        卡卡罗 雷属性<br>
                        <small>LPDDR5X 8533 / 共鸣链6级</small>
                    </div>
                </div>
            </div>
            
            <div class="control-section">
                <div class="section-title">联发科天玑旗舰</div>
                <div class="control-buttons">
                    <div class="control-btn yinlin active" data-curve="yinlin-curve">
                        吟霖 风属性<br>
                        <small>LPDDR5X 8533 / 共鸣链4级</small>
                    </div>
                    <div class="control-btn weilinaer active" data-curve="weilinaer-curve">
                        维里奈 光属性<br>
                        <small>LPDDR5X 8533 / 共鸣链2级</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const controlBtns = document.querySelectorAll('.control-btn');
            
            controlBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const curveId = this.getAttribute('data-curve');
                    const curve = document.getElementById(curveId);
                    
                    if (this.classList.contains('active')) {
                        // 隐藏曲线
                        this.classList.remove('active');
                        this.classList.add('inactive');
                        curve.classList.add('hidden');
                    } else {
                        // 显示曲线
                        this.classList.remove('inactive');
                        this.classList.add('active');
                        curve.classList.remove('hidden');
                    }
                });
            });
            
            // 初始化动画
            const curves = document.querySelectorAll('.curve-path');
            curves.forEach((curve, index) => {
                const length = curve.getTotalLength();
                curve.style.strokeDasharray = length;
                curve.style.strokeDashoffset = length;
                
                setTimeout(() => {
                    curve.style.transition = 'stroke-dashoffset 1.5s ease-in-out';
                    curve.style.strokeDashoffset = 0;
                }, index * 200);
            });
        });
    </script>
</body>
</html>
