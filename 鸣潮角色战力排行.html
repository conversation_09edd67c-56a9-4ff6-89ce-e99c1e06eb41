<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>鸣潮角色战力综合排行</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', Arial, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
            color: white;
            position: relative;
            overflow-x: hidden;
            min-height: 100vh;
        }

        /* 背景纹理效果 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                radial-gradient(circle at 25% 25%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(255, 107, 53, 0.1) 0%, transparent 50%),
                repeating-linear-gradient(
                    45deg,
                    transparent,
                    transparent 60px,
                    rgba(255,255,255,0.01) 60px,
                    rgba(255,255,255,0.01) 120px
                );
            z-index: -1;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
            position: relative;
        }

        .back-btn {
            position: absolute;
            top: 20px;
            left: 20px;
            width: 45px;
            height: 45px;
            border: 2px solid #555;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: #888;
            font-size: 20px;
            transition: all 0.3s ease;
            background: rgba(0,0,0,0.3);
            backdrop-filter: blur(10px);
        }

        .back-btn:hover {
            border-color: #00d4ff;
            color: #00d4ff;
            transform: scale(1.1);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .title {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 12px;
            background: linear-gradient(135deg, #00d4ff 0%, #0099cc 50%, #ff6b35 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 30px rgba(0, 212, 255, 0.3);
        }

        .subtitle {
            font-size: 16px;
            color: #aaa;
            margin-bottom: 20px;
            line-height: 1.5;
        }

        .update-info {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 30px;
        }

        .update-tag {
            padding: 6px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            border: 1px solid;
        }

        .update-tag.primary {
            background: rgba(0, 212, 255, 0.2);
            border-color: #00d4ff;
            color: #00d4ff;
        }

        .update-tag.secondary {
            background: rgba(255, 107, 53, 0.2);
            border-color: #ff6b35;
            color: #ff6b35;
        }

        .filter-tabs {
            display: flex;
            justify-content: center;
            gap: 12px;
            margin-bottom: 40px;
            flex-wrap: wrap;
        }

        .tab {
            padding: 10px 20px;
            border-radius: 25px;
            border: 1px solid #444;
            background: rgba(26, 26, 26, 0.8);
            color: #ccc;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .tab:hover {
            border-color: #666;
            background: rgba(40, 40, 40, 0.8);
        }

        .tab.active {
            background: linear-gradient(135deg, #ff6b35, #ff8c42);
            border-color: #ff6b35;
            color: white;
            box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
        }

        .chart-container {
            background: rgba(15, 15, 15, 0.9);
            border-radius: 15px;
            padding: 35px;
            position: relative;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            font-size: 13px;
            color: #999;
            font-weight: 500;
        }

        .ranking-item {
            display: flex;
            align-items: center;
            margin-bottom: 14px;
            position: relative;
            padding: 8px 0;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .ranking-item:hover {
            background: rgba(255, 255, 255, 0.05);
            transform: translateX(5px);
        }

        .character-info {
            width: 200px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .character-name {
            font-size: 15px;
            color: white;
            font-weight: 500;
        }

        .element-icon {
            width: 24px;
            height: 24px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 11px;
            font-weight: bold;
            box-shadow: 0 2px 8px rgba(0,0,0,0.3);
            border: 1px solid rgba(255,255,255,0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="back-btn">←</div>
        
        <h1 class="title">鸣潮角色战力综合排行</h1>
        <p class="subtitle">基于角色面板、共鸣技能、武器配置等综合评估，数据来源于游戏实测</p>
        
        <div class="filter-tabs">
            <div class="tab active">全部</div>
            <div class="tab">输出型</div>
            <div class="tab">辅助</div>
            <div class="tab">治疗</div>
            <div class="tab">坦克</div>
            <div class="tab">华彩</div>
        </div>
        
        <div class="chart-container">
            <div class="chart-header">
                <span>0</span>
                <span>125</span>
                <span>250</span>
                <span>375</span>
                <span>500</span>
            </div>
            
            <div class="ranking-item">
                <div class="character-info">
                    <div class="element-icon thunder">雷</div>
                    <span class="character-name">今汐 (满命)</span>
                </div>
                <div class="bar-container">
                    <div class="bar" style="width: 90.5%"></div>
                </div>
                <div class="score">452.6</div>
            </div>
            
            <div class="ranking-item">
                <div class="character-info">
                    <div class="element-icon ice">冰</div>
                    <span class="character-name">长离 (满命)</span>
                </div>
                <div class="bar-container">
                    <div class="bar" style="width: 87.2%"></div>
                </div>
                <div class="score">436.1</div>
            </div>
            
            <div class="ranking-item">
                <div class="character-info">
                    <div class="element-icon fire">焰</div>
                    <span class="character-name">炽霞 (6命)</span>
                </div>
                <div class="bar-container">
                    <div class="bar" style="width: 64.5%"></div>
                </div>
                <div class="score">322.6</div>
            </div>
            
            <div class="ranking-item">
                <div class="character-info">
                    <div class="element-icon thunder">雷</div>
                    <span class="character-name">卡卡罗</span>
                </div>
                <div class="bar-container">
                    <div class="bar" style="width: 64.3%"></div>
                </div>
                <div class="score">321.3</div>
            </div>
            
            <div class="ranking-item">
                <div class="character-info">
                    <div class="element-icon wind">风</div>
                    <span class="character-name">吟霖</span>
                </div>
                <div class="bar-container">
                    <div class="bar" style="width: 62.4%"></div>
                </div>
                <div class="score">311.2</div>
            </div>
            
            <div class="ranking-item">
                <div class="character-info">
                    <div class="element-icon light">光</div>
                    <span class="character-name">维里奈</span>
                </div>
                <div class="bar-container">
                    <div class="bar" style="width: 62.0%"></div>
                </div>
                <div class="score">310.2</div>
            </div>
            
            <div class="ranking-item">
                <div class="character-info">
                    <div class="element-icon ice">冰</div>
                    <span class="character-name">散华</span>
                </div>
                <div class="bar-container">
                    <div class="bar" style="width: 58.0%"></div>
                </div>
                <div class="score">289.9</div>
            </div>
            
            <div class="ranking-item">
                <div class="character-info">
                    <div class="element-icon wind">风</div>
                    <span class="character-name">秧秧</span>
                </div>
                <div class="bar-container">
                    <div class="bar" style="width: 55.8%"></div>
                </div>
                <div class="score">278.9</div>
            </div>
            
            <div class="ranking-item">
                <div class="character-info">
                    <div class="element-icon dark">暗</div>
                    <span class="character-name">渊武</span>
                </div>
                <div class="bar-container">
                    <div class="bar" style="width: 54.5%"></div>
                </div>
                <div class="score">272.3</div>
            </div>
            
            <div class="ranking-item">
                <div class="character-info">
                    <div class="element-icon fire">焰</div>
                    <span class="character-name">安可</span>
                </div>
                <div class="bar-container">
                    <div class="bar" style="width: 53.3%"></div>
                </div>
                <div class="score">266.7</div>
            </div>
            
            <div class="ranking-item">
                <div class="character-info">
                    <div class="element-icon light">光</div>
                    <span class="character-name">白芷</span>
                </div>
                <div class="bar-container">
                    <div class="bar" style="width: 52.3%"></div>
                </div>
                <div class="score">261.4</div>
            </div>
            
            <div class="ranking-item">
                <div class="character-info">
                    <div class="element-icon wind">风</div>
                    <span class="character-name">漂泊者</span>
                </div>
                <div class="bar-container">
                    <div class="bar" style="width: 48.6%"></div>
                </div>
                <div class="score">242.8</div>
            </div>
        </div>
        
        <div class="watermark">鸣潮 @共鸣者数据库</div>
    </div>
</body>
</html>
