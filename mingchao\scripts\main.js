// 全局变量
let currentData = [...charactersData];
let currentSort = { field: 'overall', direction: 'desc' };
let currentFilters = { attribute: 'all', weapon: 'all' };
let currentTab = 'ranking';

// DOM元素
const rankingTableBody = document.getElementById('rankingTableBody');
const searchInput = document.getElementById('searchInput');
const characterModal = document.getElementById('characterModal');
const modalTitle = document.getElementById('modalTitle');
const modalBody = document.getElementById('modalBody');
const modalClose = document.getElementById('modalClose');
const shareBtn = document.getElementById('shareBtn');
const exportBtn = document.getElementById('exportBtn');
const toast = document.getElementById('toast');
const toastMessage = document.getElementById('toastMessage');

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
    renderTable();
    updateURL();
});

// 事件监听器初始化
function initializeEventListeners() {
    // 导航标签
    document.querySelectorAll('.nav-tab').forEach(tab => {
        tab.addEventListener('click', function() {
            switchTab(this.dataset.tab);
        });
    });
    
    // 属性筛选
    document.querySelectorAll('[data-filter]').forEach(btn => {
        btn.addEventListener('click', function() {
            filterByAttribute(this.dataset.filter);
        });
    });
    
    // 武器筛选
    document.querySelectorAll('[data-weapon]').forEach(btn => {
        btn.addEventListener('click', function() {
            filterByWeapon(this.dataset.weapon);
        });
    });
    
    // 排序
    document.querySelectorAll('.sortable').forEach(th => {
        th.addEventListener('click', function() {
            sortTable(this.dataset.sort);
        });
    });
    
    // 搜索
    searchInput.addEventListener('input', function() {
        searchCharacters(this.value);
    });
    
    // 模态框
    modalClose.addEventListener('click', closeModal);
    characterModal.addEventListener('click', function(e) {
        if (e.target === this) closeModal();
    });
    
    // 分享和导出
    shareBtn.addEventListener('click', shareLink);
    exportBtn.addEventListener('click', exportData);
    
    // 键盘事件
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') closeModal();
    });
}

// 切换标签
function switchTab(tabName) {
    currentTab = tabName;
    
    // 更新标签状态
    document.querySelectorAll('.nav-tab').forEach(tab => {
        tab.classList.toggle('active', tab.dataset.tab === tabName);
    });
    
    // 根据标签调整排序
    switch(tabName) {
        case 'ranking':
            currentSort = { field: 'overall', direction: 'desc' };
            break;
        case 'dps':
            currentSort = { field: 'dps', direction: 'desc' };
            break;
        case 'support':
            currentSort = { field: 'support', direction: 'desc' };
            break;
        case 'survival':
            currentSort = { field: 'survival', direction: 'desc' };
            break;
    }
    
    applyFiltersAndSort();
    updateURL();
}

// 属性筛选
function filterByAttribute(attribute) {
    currentFilters.attribute = attribute;
    
    // 更新按钮状态
    document.querySelectorAll('[data-filter]').forEach(btn => {
        btn.classList.toggle('active', btn.dataset.filter === attribute);
    });
    
    applyFiltersAndSort();
    updateURL();
}

// 武器筛选
function filterByWeapon(weapon) {
    currentFilters.weapon = weapon;
    
    // 更新按钮状态
    document.querySelectorAll('[data-weapon]').forEach(btn => {
        btn.classList.toggle('active', btn.dataset.weapon === weapon);
    });
    
    applyFiltersAndSort();
    updateURL();
}

// 排序
function sortTable(field) {
    if (currentSort.field === field) {
        currentSort.direction = currentSort.direction === 'desc' ? 'asc' : 'desc';
    } else {
        currentSort.field = field;
        currentSort.direction = 'desc';
    }
    
    // 更新排序箭头
    document.querySelectorAll('.sort-arrow').forEach(arrow => {
        arrow.textContent = '';
    });
    
    const currentArrow = document.querySelector(`[data-sort="${field}"] .sort-arrow`);
    if (currentArrow) {
        currentArrow.textContent = currentSort.direction === 'desc' ? '↓' : '↑';
    }
    
    applyFiltersAndSort();
    updateURL();
}

// 搜索角色
function searchCharacters(query) {
    applyFiltersAndSort(query);
}

// 应用筛选和排序
function applyFiltersAndSort(searchQuery = '') {
    let filteredData = [...charactersData];
    
    // 应用属性筛选
    if (currentFilters.attribute !== 'all') {
        filteredData = filteredData.filter(char => char.attribute === currentFilters.attribute);
    }
    
    // 应用武器筛选
    if (currentFilters.weapon !== 'all') {
        filteredData = filteredData.filter(char => char.weapon === currentFilters.weapon);
    }
    
    // 应用搜索
    if (searchQuery.trim()) {
        const query = searchQuery.toLowerCase();
        filteredData = filteredData.filter(char => 
            char.name.toLowerCase().includes(query) ||
            char.nameEn.toLowerCase().includes(query)
        );
    }
    
    // 应用排序
    filteredData.sort((a, b) => {
        const aValue = a.scores[currentSort.field];
        const bValue = b.scores[currentSort.field];
        
        if (currentSort.direction === 'desc') {
            return bValue - aValue;
        } else {
            return aValue - bValue;
        }
    });
    
    currentData = filteredData;
    renderTable();
}

// 渲染表格
function renderTable() {
    if (currentData.length === 0) {
        rankingTableBody.innerHTML = `
            <tr>
                <td colspan="9" class="empty-state">
                    <h3>未找到匹配的角色</h3>
                    <p>请尝试调整筛选条件或搜索关键词</p>
                </td>
            </tr>
        `;
        return;
    }
    
    rankingTableBody.innerHTML = currentData.map((character, index) => {
        const rank = index + 1;
        const attribute = attributeMap[character.attribute];
        const weapon = weaponMap[character.weapon];
        
        return `
            <tr onclick="showCharacterDetail(${character.id})" data-character-id="${character.id}">
                <td class="rank-col">
                    <span class="rank-number">${rank}</span>
                </td>
                <td class="character-col">
                    <div class="character-info">
                        <div class="character-avatar" style="background: linear-gradient(135deg, ${attribute.color}, #9d4edd)">
                            ${character.name.charAt(0)}
                        </div>
                        <div class="character-details">
                            <h4>${character.name}</h4>
                            <p>${character.nameEn}</p>
                        </div>
                    </div>
                </td>
                <td class="attribute-col">
                    <span class="attribute-tag ${character.attribute}">${attribute.name}</span>
                </td>
                <td class="weapon-col">${weapon}</td>
                <td class="score-col">
                    <span class="score-display ${getScoreClass(character.scores.overall)}">${character.scores.overall}</span>
                </td>
                <td class="score-col">
                    <span class="score-display ${getScoreClass(character.scores.dps)}">${character.scores.dps}</span>
                </td>
                <td class="score-col">
                    <span class="score-display ${getScoreClass(character.scores.support)}">${character.scores.support}</span>
                </td>
                <td class="score-col">
                    <span class="score-display ${getScoreClass(character.scores.survival)}">${character.scores.survival}</span>
                </td>
                <td class="tier-col">
                    <span class="tier-badge ${character.tier}">${character.tier}</span>
                </td>
            </tr>
        `;
    }).join('');
}

// 获取评分样式类
function getScoreClass(score) {
    if (score >= 90) return 'excellent';
    if (score >= 80) return 'good';
    if (score >= 70) return 'average';
    return 'poor';
}

// 显示角色详情
function showCharacterDetail(characterId) {
    const character = charactersData.find(char => char.id === characterId);
    if (!character) return;
    
    const attribute = attributeMap[character.attribute];
    const weapon = weaponMap[character.weapon];
    
    modalTitle.textContent = `${character.name} - 角色详情`;
    modalBody.innerHTML = `
        <div class="character-detail-grid">
            <div>
                <div class="character-detail-avatar" style="background: linear-gradient(135deg, ${attribute.color}, #9d4edd)">
                    ${character.name.charAt(0)}
                </div>
            </div>
            <div class="character-detail-info">
                <h3>${character.name}</h3>
                <p><strong>英文名:</strong> ${character.nameEn}</p>
                <p><strong>属性:</strong> <span class="attribute-tag ${character.attribute}">${attribute.name}</span></p>
                <p><strong>武器:</strong> ${weapon}</p>
                <p><strong>稀有度:</strong> ${character.rarity}星</p>
                <p><strong>评级:</strong> <span class="tier-badge ${character.tier}">${character.tier}</span></p>
            </div>
        </div>
        
        <div class="character-description">
            <h4>角色介绍</h4>
            <p>${character.description}</p>
        </div>
        
        <div class="character-stats">
            <div class="stat-item">
                <div class="stat-label">综合评分</div>
                <div class="stat-value ${getScoreClass(character.scores.overall)}">${character.scores.overall}</div>
                <div class="stat-bar">
                    <div class="stat-fill" style="width: ${character.scores.overall}%"></div>
                </div>
            </div>
            <div class="stat-item">
                <div class="stat-label">DPS输出</div>
                <div class="stat-value ${getScoreClass(character.scores.dps)}">${character.scores.dps}</div>
                <div class="stat-bar">
                    <div class="stat-fill" style="width: ${character.scores.dps}%"></div>
                </div>
            </div>
            <div class="stat-item">
                <div class="stat-label">辅助能力</div>
                <div class="stat-value ${getScoreClass(character.scores.support)}">${character.scores.support}</div>
                <div class="stat-bar">
                    <div class="stat-fill" style="width: ${character.scores.support}%"></div>
                </div>
            </div>
            <div class="stat-item">
                <div class="stat-label">生存能力</div>
                <div class="stat-value ${getScoreClass(character.scores.survival)}">${character.scores.survival}</div>
                <div class="stat-bar">
                    <div class="stat-fill" style="width: ${character.scores.survival}%"></div>
                </div>
            </div>
        </div>
        
        <div class="character-skills">
            <h4>主要技能</h4>
            <ul>
                ${character.skills.map(skill => `<li>${skill}</li>`).join('')}
            </ul>
        </div>
        
        <div class="character-pros-cons">
            <div class="pros">
                <h4>优势</h4>
                <ul>
                    ${character.pros.map(pro => `<li>${pro}</li>`).join('')}
                </ul>
            </div>
            <div class="cons">
                <h4>劣势</h4>
                <ul>
                    ${character.cons.map(con => `<li>${con}</li>`).join('')}
                </ul>
            </div>
        </div>
    `;
    
    characterModal.classList.add('active');
    document.body.style.overflow = 'hidden';
}

// 关闭模态框
function closeModal() {
    characterModal.classList.remove('active');
    document.body.style.overflow = '';
}

// 分享链接
function shareLink() {
    const url = window.location.href;
    
    if (navigator.clipboard) {
        navigator.clipboard.writeText(url).then(() => {
            showToast('链接已复制到剪贴板！');
        }).catch(() => {
            showToast('复制失败，请手动复制链接');
        });
    } else {
        // 降级方案
        const textArea = document.createElement('textarea');
        textArea.value = url;
        document.body.appendChild(textArea);
        textArea.select();
        try {
            document.execCommand('copy');
            showToast('链接已复制到剪贴板！');
        } catch (err) {
            showToast('复制失败，请手动复制链接');
        }
        document.body.removeChild(textArea);
    }
}

// 导出数据
function exportData() {
    const dataToExport = {
        timestamp: new Date().toISOString(),
        version: '1.4',
        characters: currentData,
        filters: currentFilters,
        sort: currentSort
    };
    
    const blob = new Blob([JSON.stringify(dataToExport, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `wuthering_waves_ranking_${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    showToast('数据导出成功！');
}

// 显示提示消息
function showToast(message) {
    toastMessage.textContent = message;
    toast.classList.add('show');
    
    setTimeout(() => {
        toast.classList.remove('show');
    }, 3000);
}

// 更新URL
function updateURL() {
    const params = new URLSearchParams();
    
    if (currentTab !== 'ranking') params.set('tab', currentTab);
    if (currentFilters.attribute !== 'all') params.set('attr', currentFilters.attribute);
    if (currentFilters.weapon !== 'all') params.set('weapon', currentFilters.weapon);
    if (currentSort.field !== 'overall') params.set('sort', currentSort.field);
    if (currentSort.direction !== 'desc') params.set('dir', currentSort.direction);
    
    const newURL = params.toString() ? `${window.location.pathname}?${params.toString()}` : window.location.pathname;
    window.history.replaceState({}, '', newURL);
}

// 从URL加载状态
function loadFromURL() {
    const params = new URLSearchParams(window.location.search);
    
    if (params.has('tab')) {
        switchTab(params.get('tab'));
    }
    
    if (params.has('attr')) {
        filterByAttribute(params.get('attr'));
    }
    
    if (params.has('weapon')) {
        filterByWeapon(params.get('weapon'));
    }
    
    if (params.has('sort')) {
        const sortField = params.get('sort');
        const sortDir = params.get('dir') || 'desc';
        currentSort = { field: sortField, direction: sortDir };
        sortTable(sortField);
    }
}

// 页面加载时从URL恢复状态
window.addEventListener('load', loadFromURL);
