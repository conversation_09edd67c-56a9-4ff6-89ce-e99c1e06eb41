# 鸣潮角色强度排行榜

一个专业的鸣潮（Wuthering Waves）游戏角色强度分析与排行榜网站。

## 🎮 功能特色

### 核心功能
- **多维度排行榜**：综合评分、DPS输出、辅助能力、生存能力四个维度
- **智能筛选**：按属性（雷、冰、焰、风、光、暗）和武器类型筛选
- **实时搜索**：支持角色中英文名称搜索
- **详细信息**：点击角色查看详细技能、优劣势分析
- **数据导出**：支持导出当前筛选结果为JSON格式
- **链接分享**：生成可分享的链接，保持当前筛选和排序状态

### 视觉设计
- **鸣潮主题**：深色游戏风格设计，支持中文字体
- **响应式布局**：完美适配桌面和移动设备
- **交互动画**：流畅的过渡效果和视觉反馈
- **属性配色**：每个属性都有独特的颜色标识

## 🚀 快速开始

### 本地运行
1. 下载项目文件到本地
2. 使用任意HTTP服务器运行（推荐方法）：

```bash
# 使用Python（推荐）
python -m http.server 8000

# 使用Node.js
npx serve .

# 使用PHP
php -S localhost:8000
```

3. 在浏览器中访问 `http://localhost:8000`

### 直接打开
也可以直接双击 `index.html` 文件在浏览器中打开，但推荐使用HTTP服务器以获得最佳体验。

## 📊 角色数据

当前包含以下角色的详细数据：

### S级角色
- **今汐**（雷属性/剑）- 综合评分：95
- **长离**（冰属性/剑）- 综合评分：92  
- **吟霖**（风属性/变奏器）- 综合评分：90

### A级角色
- **炽霞**（火属性/枪）- 综合评分：88
- **维里奈**（光属性/变奏器）- 综合评分：87
- **卡卡罗**（雷属性/拳套）- 综合评分：85
- **安可**（火属性/变奏器）- 综合评分：83

### B级角色
- **丹瑾**（暗属性/剑）- 综合评分：82
- **白芷**（光属性/变奏器）- 综合评分：80
- **秧秧**（风属性/剑）- 综合评分：75

## 🔧 技术栈

- **前端**：HTML5 + CSS3 + JavaScript (ES6+)
- **样式**：CSS Grid + Flexbox，响应式设计
- **字体**：Noto Sans SC（中文）+ 系统字体
- **兼容性**：支持现代浏览器（Chrome 60+, Firefox 60+, Safari 12+）

## 📱 部署方案

### 免费托管平台

1. **GitHub Pages**
   - 将代码上传到GitHub仓库
   - 在仓库设置中启用GitHub Pages
   - 访问 `https://username.github.io/repository-name`

2. **Netlify**
   - 拖拽文件夹到 netlify.com
   - 自动生成链接，支持自定义域名

3. **Vercel**
   - 连接GitHub仓库或直接上传
   - 自动部署，全球CDN加速

4. **Surge.sh**
   ```bash
   npm install -g surge
   surge mingchao/
   ```

### 分享链接示例
部署后可以生成如下格式的分享链接：
- `https://your-site.com/` - 默认综合排行
- `https://your-site.com/?tab=dps&attr=thunder` - DPS排行，仅显示雷属性
- `https://your-site.com/?tab=support&weapon=rectifier` - 辅助排行，仅显示变奏器

## 🎯 使用指南

### 基本操作
1. **切换排行榜**：点击顶部标签（综合排行、DPS输出、辅助能力、生存能力）
2. **筛选角色**：使用属性和武器筛选按钮
3. **搜索角色**：在搜索框输入角色名称
4. **排序数据**：点击表头进行升序/降序排序
5. **查看详情**：点击任意角色行查看详细信息

### 高级功能
- **分享当前状态**：点击"复制链接"按钮分享当前筛选结果
- **导出数据**：点击"导出数据"按钮下载JSON格式数据
- **URL状态保持**：刷新页面或分享链接时保持当前筛选状态

## 📈 数据更新

角色数据存储在 `scripts/data.js` 文件中，可以轻松添加新角色或更新现有数据：

```javascript
{
    id: 11,
    name: "新角色",
    nameEn: "NewCharacter", 
    attribute: "thunder",
    weapon: "sword",
    rarity: 5,
    scores: {
        overall: 90,
        dps: 95,
        support: 80,
        survival: 85
    },
    tier: "S",
    description: "角色描述...",
    skills: ["技能1", "技能2", "技能3"],
    pros: ["优势1", "优势2"],
    cons: ["劣势1", "劣势2"]
}
```

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目：
- 角色数据更新
- 新功能建议  
- Bug修复
- 界面优化

## 📄 许可证

MIT License - 可自由使用、修改和分发

## 🎮 关于鸣潮

鸣潮（Wuthering Waves）是库洛游戏开发的开放世界动作RPG游戏。本排行榜基于游戏实测数据和社区贡献，仅供参考，实际游戏体验可能因个人操作和装备配置而异。

---

**更新时间**：2024-12-19  
**版本**：1.4  
**数据来源**：游戏实测 + 社区贡献
