<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>鸣潮角色DPS性能曲线</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', Arial, sans-serif;
            background: #0a0a0a;
            color: white;
            overflow-x: hidden;
            min-height: 100vh;
        }
        
        /* 背景纹理 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                repeating-linear-gradient(
                    45deg,
                    transparent,
                    transparent 40px,
                    rgba(255,255,255,0.02) 40px,
                    rgba(255,255,255,0.02) 80px
                ),
                radial-gradient(circle at 20% 20%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(255, 107, 53, 0.1) 0%, transparent 50%);
            z-index: -1;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 40px 20px;
            position: relative;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 15px;
            background: linear-gradient(135deg, #00d4ff 0%, #0099cc 50%, #ff6b35 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .update-info {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .update-tag {
            padding: 8px 20px;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 500;
            border: 1px solid;
        }
        
        .update-tag.primary {
            background: rgba(0, 212, 255, 0.2);
            border-color: #00d4ff;
            color: #00d4ff;
        }
        
        .update-tag.secondary {
            background: rgba(255, 107, 53, 0.2);
            border-color: #ff6b35;
            color: #ff6b35;
        }
        
        .chart-container {
            background: rgba(10, 10, 10, 0.95);
            border-radius: 15px;
            padding: 40px;
            position: relative;
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
        }
        
        .chart-title {
            position: absolute;
            top: 20px;
            left: 40px;
            font-size: 18px;
            color: #ccc;
            font-weight: 500;
        }
        
        .chart-logo {
            position: absolute;
            top: 20px;
            left: 120px;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #00d4ff, #ff6b35);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            color: white;
            transform: rotate(45deg);
        }
        
        .chart-logo span {
            transform: rotate(-45deg);
        }
        
        .chart-area {
            width: 100%;
            height: 600px;
            position: relative;
            margin-top: 80px;
        }
        
        .grid {
            position: absolute;
            width: 100%;
            height: 100%;
            background-image: 
                linear-gradient(to right, rgba(255,255,255,0.1) 1px, transparent 1px),
                linear-gradient(to bottom, rgba(255,255,255,0.1) 1px, transparent 1px);
            background-size: 80px 60px;
        }
        
        .axis-x, .axis-y {
            position: absolute;
            color: #888;
            font-size: 14px;
        }
        
        .axis-x {
            bottom: -30px;
            width: 100%;
            display: flex;
            justify-content: space-between;
        }
        
        .axis-y {
            left: -50px;
            height: 100%;
            display: flex;
            flex-direction: column-reverse;
            justify-content: space-between;
            align-items: flex-end;
        }
        
        .axis-label-x {
            position: absolute;
            bottom: -60px;
            right: 0;
            color: #888;
            font-size: 16px;
        }
        
        .axis-label-y {
            position: absolute;
            left: -80px;
            top: 0;
            color: #888;
            font-size: 16px;
            writing-mode: vertical-rl;
            text-orientation: mixed;
        }
        
        .curve {
            position: absolute;
            width: 100%;
            height: 100%;
        }
        
        .curve svg {
            width: 100%;
            height: 100%;
        }
        
        .curve path {
            fill: none;
            stroke-width: 3;
            filter: drop-shadow(0 0 8px currentColor);
        }
        
        .legend {
            position: absolute;
            top: 100px;
            right: 40px;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 10px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            font-size: 12px;
        }
        
        .legend-color {
            width: 20px;
            height: 3px;
            margin-right: 8px;
            border-radius: 2px;
        }
        
        .data-point {
            position: absolute;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            border: 2px solid;
            background: #0a0a0a;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .data-point:hover {
            transform: scale(1.5);
        }
        
        .tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            pointer-events: none;
            z-index: 1000;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">鸣潮角色DPS性能曲线分析</h1>
            
            <div class="update-info">
                <div class="update-tag primary">更新日志</div>
                <div class="update-tag secondary">查看 1.4版本 角色数据</div>
            </div>
        </div>
        
        <div class="chart-container">
            <div class="chart-title">DPS输出 多核 (万)</div>
            <div class="chart-logo">
                <span>鸣潮<br>数据</span>
            </div>
            
            <div class="chart-area">
                <div class="grid"></div>
                
                <div class="axis-y">
                    <span>120</span>
                    <span>100</span>
                    <span>80</span>
                    <span>60</span>
                    <span>40</span>
                    <span>20</span>
                    <span>0</span>
                </div>
                
                <div class="axis-x">
                    <span>0</span>
                    <span>2</span>
                    <span>4</span>
                    <span>6</span>
                    <span>8</span>
                    <span>10</span>
                    <span>12</span>
                    <span>14</span>
                    <span>16</span>
                    <span>18</span>
                    <span>20</span>
                    <span>22</span>
                </div>
                
                <div class="axis-label-x">共鸣链数 (链)</div>
                <div class="axis-label-y">DPS输出 (万)</div>
                
                <div class="curve">
                    <svg viewBox="0 0 1000 500">
                        <!-- 今汐曲线 -->
                        <path d="M 50 450 Q 150 400 250 350 Q 400 280 550 220 Q 700 180 850 150 Q 900 140 950 135" 
                              stroke="#00d4ff" class="curve-line"/>
                        
                        <!-- 长离曲线 -->
                        <path d="M 50 460 Q 150 420 250 370 Q 400 300 550 240 Q 700 200 850 170 Q 900 160 950 155" 
                              stroke="#4cc9f0" class="curve-line"/>
                        
                        <!-- 炽霞曲线 -->
                        <path d="M 50 470 Q 150 440 250 400 Q 400 340 550 280 Q 700 240 850 210 Q 900 200 950 195" 
                              stroke="#f72585" class="curve-line"/>
                        
                        <!-- 卡卡罗曲线 -->
                        <path d="M 50 480 Q 150 450 250 420 Q 400 360 550 300 Q 700 260 850 230 Q 900 220 950 215" 
                              stroke="#9d4edd" class="curve-line"/>
                    </svg>
                </div>
                
                <div class="legend">
                    <div class="legend-item">
                        <div class="legend-color" style="background: #00d4ff;"></div>
                        <span>今汐 (雷属性)</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: #4cc9f0;"></div>
                        <span>长离 (冰属性)</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: #f72585;"></div>
                        <span>炽霞 (焰属性)</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: #9d4edd;"></div>
                        <span>卡卡罗 (雷属性)</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 添加交互效果
        document.addEventListener('DOMContentLoaded', function() {
            const curves = document.querySelectorAll('.curve-line');
            
            // 曲线动画
            curves.forEach((curve, index) => {
                const length = curve.getTotalLength();
                curve.style.strokeDasharray = length;
                curve.style.strokeDashoffset = length;
                
                setTimeout(() => {
                    curve.style.transition = 'stroke-dashoffset 2s ease-in-out';
                    curve.style.strokeDashoffset = 0;
                }, index * 300);
            });
        });
    </script>
</body>
</html>
