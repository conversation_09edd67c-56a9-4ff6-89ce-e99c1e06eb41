// 鸣潮角色数据
const charactersData = [
    {
        id: 1,
        name: "今汐",
        nameEn: "Jin<PERSON>",
        attribute: "thunder",
        weapon: "sword",
        rarity: 5,
        scores: {
            overall: 95,
            dps: 98,
            support: 85,
            survival: 90
        },
        tier: "S",
        description: "雷属性主C角色，拥有极高的单体和群体输出能力，是当前版本最强的DPS角色之一。",
        skills: [
            "雷鸣千军 - 强力雷属性攻击",
            "电光石火 - 瞬移突进技能",
            "雷霆万钧 - 大范围雷属性爆发"
        ],
        pros: [
            "极高的DPS输出",
            "优秀的机动性",
            "强力的爆发能力",
            "适应性强"
        ],
        cons: [
            "需要较高的操作技巧",
            "对装备要求较高"
        ]
    },
    {
        id: 2,
        name: "长离",
        nameEn: "Changli",
        attribute: "ice",
        weapon: "sword",
        rarity: 5,
        scores: {
            overall: 92,
            dps: 95,
            support: 80,
            survival: 88
        },
        tier: "S",
        description: "冰属性剑士，拥有优雅的战斗风格和强大的冰属性控制能力。",
        skills: [
            "霜华剑舞 - 连续冰属性斩击",
            "冰封千里 - 大范围冰冻控制",
            "绝对零度 - 终极冰属性爆发"
        ],
        pros: [
            "强力的控制能力",
            "稳定的输出",
            "优秀的生存能力"
        ],
        cons: [
            "技能CD较长",
            "对位置要求较高"
        ]
    },
    {
        id: 3,
        name: "炽霞",
        nameEn: "Chixia",
        attribute: "fire",
        weapon: "gun",
        rarity: 4,
        scores: {
            overall: 88,
            dps: 90,
            support: 75,
            survival: 85
        },
        tier: "A",
        description: "火属性枪手，擅长远程输出和火属性持续伤害。",
        skills: [
            "烈焰射击 - 火属性远程攻击",
            "燃烧弹幕 - 持续火属性伤害",
            "炽热风暴 - 大范围火属性爆发"
        ],
        pros: [
            "安全的远程输出",
            "持续伤害能力强",
            "操作相对简单"
        ],
        cons: [
            "机动性较差",
            "单次爆发不足"
        ]
    },
    {
        id: 4,
        name: "卡卡罗",
        nameEn: "Kakaro",
        attribute: "thunder",
        weapon: "gauntlet",
        rarity: 5,
        scores: {
            overall: 85,
            dps: 82,
            support: 95,
            survival: 88
        },
        tier: "A",
        description: "雷属性拳套使用者，主要担任辅助角色，提供强力的团队增益。",
        skills: [
            "雷电拳击 - 雷属性近战攻击",
            "电磁护盾 - 团队防护技能",
            "雷鸣激励 - 团队增益技能"
        ],
        pros: [
            "优秀的辅助能力",
            "强力的团队增益",
            "良好的生存能力"
        ],
        cons: [
            "个人输出有限",
            "依赖团队配合"
        ]
    },
    {
        id: 5,
        name: "吟霖",
        nameEn: "Yinlin",
        attribute: "wind",
        weapon: "rectifier",
        rarity: 5,
        scores: {
            overall: 90,
            dps: 88,
            support: 92,
            survival: 85
        },
        tier: "S",
        description: "风属性变奏器使用者，拥有独特的音律攻击方式和强力的群体控制。",
        skills: [
            "风之协奏 - 风属性音波攻击",
            "暴风序曲 - 群体控制技能",
            "天籁之音 - 团队治疗与增益"
        ],
        pros: [
            "强力的群体控制",
            "优秀的辅助能力",
            "独特的攻击方式"
        ],
        cons: [
            "学习曲线较陡",
            "需要精确的时机把握"
        ]
    },
    {
        id: 6,
        name: "维里奈",
        nameEn: "Weilinaer",
        attribute: "light",
        weapon: "rectifier",
        rarity: 5,
        scores: {
            overall: 87,
            dps: 85,
            support: 90,
            survival: 88
        },
        tier: "A",
        description: "光属性变奏器使用者，专精治疗和支援，是团队的重要后勤保障。",
        skills: [
            "圣光治愈 - 强力治疗技能",
            "光明护佑 - 团队防护增益",
            "神圣审判 - 光属性攻击技能"
        ],
        pros: [
            "强力的治疗能力",
            "优秀的团队支援",
            "稳定的生存保障"
        ],
        cons: [
            "输出能力有限",
            "过于依赖团队"
        ]
    },
    {
        id: 7,
        name: "安可",
        nameEn: "Anke",
        attribute: "fire",
        weapon: "rectifier",
        rarity: 5,
        scores: {
            overall: 83,
            dps: 88,
            support: 70,
            survival: 80
        },
        tier: "A",
        description: "火属性变奏器使用者，年轻但实力不俗的输出型角色。",
        skills: [
            "烈焰乐章 - 火属性音波攻击",
            "燃烧节拍 - 持续火属性伤害",
            "炽热终章 - 火属性爆发技能"
        ],
        pros: [
            "稳定的火属性输出",
            "较好的群体伤害",
            "技能连携流畅"
        ],
        cons: [
            "生存能力一般",
            "缺乏控制手段"
        ]
    },
    {
        id: 8,
        name: "白芷",
        nameEn: "Baizhi",
        attribute: "light",
        weapon: "rectifier",
        rarity: 4,
        scores: {
            overall: 80,
            dps: 70,
            support: 95,
            survival: 85
        },
        tier: "B",
        description: "光属性治疗师，专精团队治疗和状态恢复。",
        skills: [
            "治愈之光 - 基础治疗技能",
            "净化光环 - 状态净化技能",
            "生命祝福 - 团队生命值提升"
        ],
        pros: [
            "强力的治疗能力",
            "状态净化功能",
            "团队生存保障"
        ],
        cons: [
            "几乎没有输出能力",
            "战斗贡献单一"
        ]
    },
    {
        id: 9,
        name: "秧秧",
        nameEn: "Yangyang",
        attribute: "wind",
        weapon: "sword",
        rarity: 4,
        scores: {
            overall: 75,
            dps: 78,
            support: 70,
            survival: 75
        },
        tier: "B",
        description: "风属性剑士，平衡型角色，适合新手使用。",
        skills: [
            "风刃斩 - 风属性剑技",
            "疾风步 - 位移技能",
            "风暴剑舞 - 群体攻击技能"
        ],
        pros: [
            "操作简单易上手",
            "技能平衡实用",
            "适合新手练习"
        ],
        cons: [
            "各方面能力平庸",
            "后期成长有限"
        ]
    },
    {
        id: 10,
        name: "丹瑾",
        nameEn: "Danjin",
        attribute: "dark",
        weapon: "sword",
        rarity: 4,
        scores: {
            overall: 82,
            dps: 85,
            support: 65,
            survival: 78
        },
        tier: "B",
        description: "暗属性剑士，拥有独特的生命消耗机制和高爆发输出。",
        skills: [
            "暗影剑技 - 暗属性攻击",
            "血之契约 - 消耗生命提升攻击",
            "暗夜终结 - 高伤害爆发技能"
        ],
        pros: [
            "高爆发输出潜力",
            "独特的战斗机制",
            "技能特效华丽"
        ],
        cons: [
            "生存能力较差",
            "操作难度较高",
            "容错率低"
        ]
    }
];

// 属性映射
const attributeMap = {
    thunder: { name: "雷", color: "#00d4ff" },
    ice: { name: "冰", color: "#4cc9f0" },
    fire: { name: "焰", color: "#f72585" },
    wind: { name: "风", color: "#43aa8b" },
    light: { name: "光", color: "#f9c74f" },
    dark: { name: "暗", color: "#9d4edd" }
};

// 武器映射
const weaponMap = {
    sword: "剑",
    gun: "枪",
    gauntlet: "拳套",
    rectifier: "变奏器",
    broadblade: "阔刀"
};

// 评级映射
const tierMap = {
    S: { name: "S", color: "#ffd700" },
    A: { name: "A", color: "#ff6b35" },
    B: { name: "B", color: "#4cc9f0" },
    C: { name: "C", color: "#43aa8b" }
};

// 导出数据
window.charactersData = charactersData;
window.attributeMap = attributeMap;
window.weaponMap = weaponMap;
window.tierMap = tierMap;
